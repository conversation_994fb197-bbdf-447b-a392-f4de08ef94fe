import React from 'react';
import { useDesigner } from '../../context/DesignerContext';

export interface ToolbarProps {
  height?: number;
  style?: React.CSSProperties;
  className?: string;
}

export const Toolbar: React.FC<ToolbarProps> = ({ height = 48, style, className }) => {
  const { canvasState, setCanvasMode, schema } = useDesigner();

  const defaultStyle: React.CSSProperties = {
    height,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '0 12px',
    background: '#fff',
    borderBottom: '1px solid #e8e8e8',
    ...style
  };

  const togglePreview = () => {
    setCanvasMode(canvasState.mode === 'design' ? 'preview' : 'design');
  };

  const exportSchema = () => {
    const data = JSON.stringify(schema, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${schema.title || 'page'}.schema.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className={`lowcode-toolbar ${className || ''}`} style={defaultStyle}>
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        <button onClick={togglePreview}>
          {canvasState.mode === 'design' ? '进入预览' : '返回设计'}
        </button>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        <button onClick={exportSchema}>导出 Schema</button>
      </div>
    </div>
  );
};

