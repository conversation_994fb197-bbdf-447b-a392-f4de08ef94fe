import React from 'react';
import { tokens } from '../../theme/tokens';

export interface TopNavigationProps extends React.HTMLAttributes<HTMLElement> {
  logo?: {
    src?: string;
    alt?: string;
    text?: string;
    href?: string;
  };
  menu?: Array<{
    key: string;
    label: string;
    href?: string;
    children?: Array<{
      key: string;
      label: string;
      href?: string;
    }>;
  }>;
  user?: {
    name?: string;
    avatar?: string;
    menu?: Array<{
      key: string;
      label: string;
      onClick?: () => void;
    }>;
  };
  actions?: Array<{
    key: string;
    label: string;
    icon?: string;
    onClick?: () => void;
  }>;
  style?: React.CSSProperties;
  className?: string;
  onMenuClick?: (key: string) => void;
  onUserMenuClick?: (key: string) => void;
}

export const TopNavigation: React.FC<TopNavigationProps> = ({
  logo,
  menu = [],
  user,
  actions = [],
  style,
  className,
  onMenuClick,
  onUserMenuClick,
  ...rest
}) => {
  const [activeMenu, setActiveMenu] = React.useState<string>('');
  const [showUserMenu, setShowUserMenu] = React.useState(false);

  const handleMenuClick = (key: string, href?: string) => {
    setActiveMenu(key);
    if (href) {
      window.location.href = href;
    }
    onMenuClick?.(key);
  };

  const handleUserMenuClick = (key: string) => {
    setShowUserMenu(false);
    onUserMenuClick?.(key);
  };

  const defaultStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '0 20px',
    height: '56px',
    backgroundColor: tokens.color.bg,
    color: tokens.color.text,
    borderBottom: `1px solid ${tokens.color.border}`,
    ...style,
  };

  return (
    <nav className={`lowcode-top-navigation ${className || ''}`} style={defaultStyle} {...rest}>
      {/* Logo区域 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {logo && (
          <div style={{ display: 'flex', alignItems: 'center', marginRight: '32px' }}>
            {logo.href ? (
              <a
                href={logo.href}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  textDecoration: 'none',
                  color: 'inherit',
                }}
              >
                {logo.src && (
                  <img
                    src={logo.src}
                    alt={logo.alt || 'Logo'}
                    style={{ height: '32px', marginRight: logo.text ? '12px' : '0' }}
                  />
                )}
                {logo.text && (
                  <span style={{ fontSize: '14px', fontWeight: 600 }}>
                    {logo.text}
                  </span>
                )}
              </a>
            ) : (
              <>
                {logo.src && (
                  <img
                    src={logo.src}
                    alt={logo.alt || 'Logo'}
                    style={{ height: '32px', marginRight: logo.text ? '12px' : '0' }}
                  />
                )}
                {logo.text && (
                  <span style={{ fontSize: '14px', fontWeight: 600 }}>
                    {logo.text}
                  </span>
                )}
              </>
            )}
          </div>
        )}

        {/* 主菜单 */}
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {menu.map((item) => (
            <div key={item.key} style={{ position: 'relative', marginRight: '8px' }}>
              <button
                style={{
                  background: activeMenu === item.key ? tokens.color.blue50 : 'transparent',
                  border: '1px solid',
                  borderColor: activeMenu === item.key ? tokens.color.blue100 : 'transparent',
                  color: activeMenu === item.key ? '#2563eb' : '#374151',
                  fontSize: '14px',
                  padding: '8px 10px',
                  cursor: 'pointer',
                  borderRadius: `${tokens.radius.sm}px`,
                  transition: 'all 0.2s',
                }}
                onClick={() => handleMenuClick(item.key, item.href)}
                onMouseEnter={(e) => {
                  if (!item.href) {
                    e.currentTarget.style.backgroundColor = '#f8fafc';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!item.href) {
                    e.currentTarget.style.backgroundColor = activeMenu === item.key ? 'rgba(59,130,246,0.1)' : 'transparent';
                  }
                }}
              >
                {item.label}
              </button>

              {/* 子菜单 */}
              {item.children && item.children.length > 0 && (
                <div
                  style={{
                    position: 'absolute',
                    top: '100%',
                    left: '0',
                    backgroundColor: '#ffffff',
                    boxShadow: '0 10px 24px rgba(0, 0, 0, 0.12)',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    minWidth: '200px',
                    zIndex: 1000,
                    padding: '6px',
                    display: activeMenu === item.key ? 'block' : 'none',
                  }}
                >
                  {item.children.map((child) => (
                    <button
                      key={child.key}
                      className={"lc-btn lc-btn-default"}
                      style={{ display: 'block', width: '100%', textAlign: 'left', background: 'transparent', border: 'none', padding: '8px 10px' }}
                      onClick={() => handleMenuClick(child.key, child.href)}
                    >
                      {child.label}
                    </button>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 右侧区域 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {/* 操作按钮 */}
        {actions.map((action) => (
          <button
            key={action.key}
            className={"lc-btn lc-btn-default"}
            style={{ marginRight: '8px' }}
            onClick={action.onClick}
          >
            {action.icon && <span style={{ marginRight: '6px' }}>{action.icon}</span>}
            {action.label}
          </button>
        ))}

        {/* 用户信息 */}
        {user && (
          <div style={{ position: 'relative' }}>
            <button
              className={"lc-btn lc-btn-default"}
              style={{ display: 'flex', alignItems: 'center', borderRadius: '20px', padding: '6px 10px' }}
              onClick={() => setShowUserMenu(!showUserMenu)}
            >
              {user.avatar && (
                <img
                  src={user.avatar}
                  alt="User Avatar"
                  style={{
                    width: '24px',
                    height: '24px',
                    borderRadius: '50%',
                    marginRight: user.name ? '8px' : '0',
                  }}
                />
              )}
              {user.name && <span>{user.name}</span>}
            </button>

            {/* 用户菜单 */}
            {user.menu && user.menu.length > 0 && showUserMenu && (
              <div
                style={{
                  position: 'absolute',
                  top: '100%',
                  right: '0',
                  backgroundColor: '#ffffff',
                  boxShadow: '0 10px 24px rgba(0, 0, 0, 0.12)',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  minWidth: '200px',
                  zIndex: 1000,
                }}
              >
                {user.menu.map((item) => (
                  <button
                    key={item.key}
                    style={{
                      display: 'block',
                      width: '100%',
                      padding: '8px 16px',
                      border: 'none',
                      background: 'none',
                      textAlign: 'left',
                      color: '#000000',
                      fontSize: '14px',
                      cursor: 'pointer',
                      transition: 'background-color 0.3s',
                    }}
                    onClick={() => {
                      item.onClick?.();
                      handleUserMenuClick(item.key);
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f5f5f5';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                  >
                    {item.label}
                  </button>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </nav>
  );
};
