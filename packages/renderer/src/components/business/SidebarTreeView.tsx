import React from 'react';

export interface TreeNode {
  key: string;
  title: string;
  icon?: string;
  href?: string;
  children?: TreeNode[];
  disabled?: boolean;
}

export interface SidebarTreeViewProps extends React.HTMLAttributes<HTMLDivElement> {
  data: TreeNode[];
  width?: number | string;
  height?: number | string;
  searchable?: boolean;
  searchPlaceholder?: string;
  defaultExpandedKeys?: string[];
  defaultSelectedKeys?: string[];
  showIcon?: boolean;
  style?: React.CSSProperties;
  className?: string;
  onSelect?: (selectedKeys: string[], node: TreeNode) => void;
  onExpand?: (expandedKeys: string[], node: TreeNode) => void;
  onSearch?: (value: string) => void;
}

export const SidebarTreeView: React.FC<SidebarTreeViewProps> = ({
  data,
  width = 240,
  height = '100%',
  searchable = true,
  searchPlaceholder = '搜索...',
  defaultExpandedKeys = [],
  defaultSelectedKeys = [],
  showIcon = true,
  style,
  className,
  onSelect,
  onExpand,
  onSearch,
  ...rest
}) => {
  const [expandedKeys, setExpandedKeys] = React.useState<string[]>(defaultExpandedKeys);
  const [selectedKeys, setSelectedKeys] = React.useState<string[]>(defaultSelectedKeys);
  const [searchValue, setSearchValue] = React.useState<string>('');
  const [filteredData, setFilteredData] = React.useState<TreeNode[]>(data);

  // 搜索功能
  React.useEffect(() => {
    if (!searchValue) {
      setFilteredData(data);
      return;
    }

    const filterTree = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.reduce((acc: TreeNode[], node) => {
        const matchesSearch = node.title.toLowerCase().includes(searchValue.toLowerCase());
        const filteredChildren = node.children ? filterTree(node.children) : [];

        if (matchesSearch || filteredChildren.length > 0) {
          acc.push({
            ...node,
            children: filteredChildren.length > 0 ? filteredChildren : node.children
          });
        }

        return acc;
      }, []);
    };

    const filtered = filterTree(data);
    setFilteredData(filtered);

    // 搜索时自动展开所有匹配的节点
    if (searchValue) {
      const getAllKeys = (nodes: TreeNode[]): string[] => {
        let keys: string[] = [];
        nodes.forEach(node => {
          keys.push(node.key);
          if (node.children) {
            keys = keys.concat(getAllKeys(node.children));
          }
        });
        return keys;
      };
      setExpandedKeys(getAllKeys(filtered));
    }
  }, [searchValue, data]);

  const handleNodeClick = (node: TreeNode) => {
    if (node.disabled) return;

    // 处理选中状态
    const newSelectedKeys = [node.key];
    setSelectedKeys(newSelectedKeys);
    onSelect?.(newSelectedKeys, node);

    // 如果有链接，进行跳转
    if (node.href) {
      window.location.href = node.href;
    }
  };

  const handleNodeExpand = (node: TreeNode) => {
    const isExpanded = expandedKeys.includes(node.key);
    let newExpandedKeys: string[];

    if (isExpanded) {
      newExpandedKeys = expandedKeys.filter(key => key !== node.key);
    } else {
      newExpandedKeys = [...expandedKeys, node.key];
    }

    setExpandedKeys(newExpandedKeys);
    onExpand?.(newExpandedKeys, node);
  };

  const handleSearch = (value: string) => {
    setSearchValue(value);
    onSearch?.(value);
  };

  const renderTreeNode = (node: TreeNode, level: number = 0): React.ReactElement => {
    const isExpanded = expandedKeys.includes(node.key);
    const isSelected = selectedKeys.includes(node.key);
    const hasChildren = node.children && node.children.length > 0;
    const paddingLeft = level * 20 + 16;

    return (
      <div key={node.key}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            padding: '8px 12px',
            paddingLeft: `${paddingLeft}px`,
            cursor: node.disabled ? 'not-allowed' : 'pointer',
            backgroundColor: isSelected ? '#eef5ff' : 'transparent',
            color: node.disabled ? '#9ca3af' : isSelected ? '#1d4ed8' : '#374151',
            borderLeft: isSelected ? '3px solid #3b82f6' : '3px solid transparent',
            transition: 'all 0.2s',
            fontSize: '14px',
            userSelect: 'none',
            borderRadius: '6px',
            margin: '2px 6px',
          }}
          onClick={() => handleNodeClick(node)}
          onMouseEnter={(e) => {
            if (!node.disabled && !isSelected) {
              e.currentTarget.style.backgroundColor = '#f8fafc';
            }
          }}
          onMouseLeave={(e) => {
            if (!node.disabled && !isSelected) {
              e.currentTarget.style.backgroundColor = 'transparent';
            }
          }}
        >
          {/* 展开/收起图标 */}
          {hasChildren && (
            <span
              style={{
                marginRight: '8px',
                fontSize: '12px',
                transition: 'transform 0.3s',
                transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
                cursor: 'pointer',
              }}
              onClick={(e) => {
                e.stopPropagation();
                handleNodeExpand(node);
              }}
            >
              ▶
            </span>
          )}

          {/* 占位符，保持对齐 */}
          {!hasChildren && <span style={{ marginRight: '20px' }} />}

          {/* 节点图标 */}
          {showIcon && node.icon && (
            <span style={{ marginRight: '8px', fontSize: '16px' }}>
              {node.icon}
            </span>
          )}

          {/* 节点标题 */}
          <span style={{ flex: 1 }}>{node.title}</span>
        </div>

        {/* 子节点 */}
        {hasChildren && isExpanded && (
          <div>
            {node.children!.map(child => renderTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const defaultStyle: React.CSSProperties = {
    width,
    height,
    backgroundColor: '#ffffff',
    borderRight: '1px solid #e5e7eb',
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column',
    ...style,
  };

  return (
    <div className={`lowcode-sidebar-tree-view ${className || ''}`} style={defaultStyle} {...rest}>
      {/* 搜索框 */}
      {searchable && (
        <div style={{ padding: '12px', borderBottom: '1px solid #e5e7eb', background: '#f9fafb' }}>
          <input
            type="text"
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={(e) => handleSearch(e.target.value)}
            style={{
              width: '100%',
              height: '32px',
              padding: '4px 10px',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              fontSize: '12px',
              outline: 'none',
              background: '#fff',
              transition: 'all 0.2s',
            }}
            onFocus={(e) => {
              e.currentTarget.style.borderColor = '#93c5fd';
              e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59,130,246,0.15)';
            }}
            onBlur={(e) => {
              e.currentTarget.style.borderColor = '#e5e7eb';
              e.currentTarget.style.boxShadow = 'none';
            }}
          />
        </div>
      )}

      {/* 树形结构 */}
      <div style={{ flex: 1, overflow: 'auto' }}>
        {filteredData.length > 0 ? (
          filteredData.map(node => renderTreeNode(node))
        ) : (
          <div
            style={{
              padding: '20px',
              textAlign: 'center',
              color: '#bfbfbf',
              fontSize: '14px',
            }}
          >
            {searchValue ? '没有找到匹配的结果' : '暂无数据'}
          </div>
        )}
      </div>
    </div>
  );
};
